{"name": "@digisac/back", "version": "3.62.0-mr-2620.4", "private": true, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "main": "index", "scripts": {"nodemon": "nodemon $*", "run-live": "$NODE_DEBUG_OPTION ts-node-dev --transpile-only --respawn --exit-child", "run-static": "$NODE_DEBUG_OPTION ts-node --transpile-only", "run-live-babel": "nodemon -e ts,js -x 'env DEBUG=app:* $NODE_DEBUG_OPTION babel-node --extensions \".ts,.js\"'", "run-static-babel": "$NODE_DEBUG_OPTION babel-node --extensions \".ts,.js\"", "jest": "node --trace-warnings ./node_modules/.bin/jest --runInBand --detectOpenHandles --forceExit --verbose=true $*", "develop": "node src/scripts/develop", "test:unit": "yarn jest --coverage src/__tests__/unit $*", "test:e2e": "yarn jest src/__tests__/e2e $*", "test:integration": "yarn jest src/__tests__/integration $*", "test:all": "yarn jest $*", "test": "yarn test:all $*", "test:watch": "yarn test:all --watch $*", "sequelize": "sequelize $*", "scripts": "babel-node --extensions \".ts,.js\" src/scripts", "app": "yarn scripts", "app:dist": "node dist/scripts", "reset-db-for-test": "env NODE_ENV=cypress yarn app db:tests:reset", "develop-for-test": "env NODE_ENV=cypress yarn develop -s api,socket,bot,whatsapp", "build": "tsc-transpile-only --project tsconfig.json --outDir ./dist", "dc:logs": "docker-compose logs --tail 500 -f $*", "dc:exec": "docker exec app-api sh -c $*", "dc:res": "docker-compose restart $*", "develop:whatsappBusinessMock": "nodemon src/microServices/whatsappBusiness/mock/server", "format": "prettier \"$PWD/src/**/*.{js,ts,jsx,tsx,css,scss,json}\" --write", "format-all": "yarn format \"$PWD/src/**/*.{js,ts,jsx,tsx,css,scss,json}\"", "lint": "eslint", "lint-all": "eslint src"}, "dependencies": {"@azure/msal-node": "3.2.3", "@babel/plugin-transform-object-assign": "^7.24.1", "@babel/plugin-transform-runtime": "^7.24.3", "@digisac/wa-version": "2.3000.1025795614", "@elastic/ecs-winston-format": "1.5.3", "@ffmpeg-installer/ffmpeg": "1.1.0", "@node-oauth/oauth2-server": "^5.2.0", "@sentry/node": "7.114.0", "@wppconnect/wa-version": "1.5.2015", "@zxcvbn-ts/core": "3.0.4", "@zxcvbn-ts/language-common": "3.0.4", "app-root-path": "3.1.0", "archiver": "6.0.1", "assign-deep": "1.0.1", "async": "3.2.5", "aws-sdk": "2.1620.0", "axios": "1.6.8", "bcrypt": "5.1.1", "body-parser": "1.20.2", "businessmessages": "^1.0.4", "chalk": "~4.1.2", "commander": "11.1.0", "content-disposition": "^0.5.4", "cors": "2.8.5", "cryptr": "^6.3.0", "csv-parse": "^5.5.5", "csv-stringify": "6.5.0", "date-fns": "2.30.0", "death": "1.1.0", "dotenv": "16.4.5", "elastic-apm-node": "~3.51.0", "excel4node": "^1.8.2", "expo-server-sdk": "^3.7.0", "express": "4.19.2", "express-oas-generator": "1.0.24", "express-oauth-server": "2.0.0", "express-routemap": "1.6.0", "faker": "~5.5.3", "fast-deep-equal": "3.1.3", "ffprobe": "1.1.2", "file-type": "~16.5.4", "flatted": "3.3.1", "fluent-ffmpeg": "2.1.2", "form-data": "4.0.0", "fs": "^0.0.1-security", "fs-jetpack": "5.1.0", "googleapis": "^137.1.0", "helmet": "7.1.0", "html-to-text": "9.0.5", "http-proxy": "1.18.1", "http-terminator": "3.2.0", "i18next": "^23.14.0", "image-size-from-base64": "^1.0.7", "imap-simple": "^5.1.0", "inquirer": "~8.2.6", "ioredis": "^5.4.1", "jimp": "0.22.12", "jsonwebtoken": "^9.0.2", "libphonenumber-js": "^1.11.4", "lodash": "4.17.21", "mailparser": "3.7.1", "md5": "2.3.0", "micromatch": "4.0.5", "mime-types": "2.1.35", "moize": "6.1.6", "moment": "^2.30.1", "mongoose": "^8.3.5", "mongoose-delete": "^1.0.2", "morgan": "1.10.0", "multer": "^1.4.4", "node-cache": "5.1.2", "node-schedule": "^2.1.1", "node-telegram-bot-api": "^0.65.1", "nodemailer": "^6.9.13", "omit-deep-lodash": "^1.1.7", "openai": "^4.47.2", "otplib": "^12.0.1", "pdf-parse": "^1.1.1", "pg": "8.11.5", "pg-hstore": "2.3.4", "promise-timeout": "1.3.0", "puppeteer-core": "22.8.2", "qs": "6.12.1", "random-seed": "0.3.0", "redis-semaphore": "^5.5.1", "redlock": "^5.0.0-beta.2", "reflect-metadata": "0.2.2", "rimraf": "5.0.7", "safe-stable-stringify": "^2.4.3", "sequelize": "6.37.3", "sequelize-cli": "6.6.2", "serialize-error": "~8.1.0", "sharp": "^0.33.5", "socket.io": "4.7.5", "socket.io-client": "4.7.5", "socket.io-proxy": "^1.0.3", "socketio-wildcard": "2.0.0", "stack-trace": "0.0.10", "stream-array": "1.1.2", "streamify": "^1.0.0", "tar": "6.2.0", "thumbsupply": "^0.4.0", "timezones.json": "1.7.1", "tiny-worker": "2.3.0", "tree-kill": "1.2.2", "typedi": "0.10.0", "uuid": "9.0.1", "validator": "^13.12.0", "whatsapp-scripts-dist": "git+https://gitlab+deploy-token-1:<EMAIL>/whatsapp-scripts/whatsapp-scripts-dist#v1.306.0", "winston": "^3.13.0", "workerpool": "^9.1.1", "xoauth2": "^1.2.0", "zlib": "1.0.5", "zod": "^3.23.8"}, "devDependencies": {"@babel/cli": "7.24.5", "@babel/core": "7.24.5", "@babel/node": "7.23.9", "@babel/plugin-proposal-class-properties": "7.18.6", "@babel/plugin-proposal-decorators": "7.24.1", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-private-methods": "7.18.6", "@babel/plugin-proposal-throw-expressions": "7.24.1", "@babel/preset-env": "7.24.5", "@babel/preset-typescript": "7.24.1", "@babel/register": "7.23.7", "@types/async": "3.2.24", "@types/axios": "^0.14.0", "@types/chalk": "^2.2.0", "@types/cls-hooked": "4.3.8", "@types/cryptr": "^4.0.3", "@types/date-fns": "2.6.0", "@types/express": "^5.0.0", "@types/express-oauth-server": "2.0.7", "@types/faker": "6.6.9", "@types/fluent-ffmpeg": "2.1.24", "@types/html-to-text": "^9.0.4", "@types/http-proxy": "^1.17.14", "@types/imap-simple": "^4.2.9", "@types/inquirer": "~8.2.6", "@types/ioredis": "^5.0.0", "@types/jest": "29.5.12", "@types/jsonwebtoken": "^9.0.6", "@types/lodash": "^4.17.1", "@types/md5": "2.3.5", "@types/micromatch": "4.0.7", "@types/mime-types": "^2.1.4", "@types/multer": "^1.4.11", "@types/node": "^20.12.12", "@types/node-schedule": "2.1.7", "@types/node-telegram-bot-api": "0.64.6", "@types/nodemailer": "^6.4.15", "@types/omit-deep-lodash": "^1.1.3", "@types/promise-timeout": "^1.3.3", "@types/puppeteer-core": "7.0.4", "@types/random-seed": "0.3.5", "@types/redlock": "^4.0.7", "@types/sequelize": "4.28.20", "@types/socket.io": "3.0.2", "@types/socket.io-client": "3.0.0", "@types/stack-trace": "0.0.33", "@types/tar": "^6.1.13", "@types/uuid": "9.0.8", "@types/workerpool": "^6.4.7", "@typescript-eslint/eslint-plugin": "6.13.2", "@typescript-eslint/parser": "6.13.2", "babel-plugin-transform-typescript-metadata": "0.3.2", "concurrently": "8.2.2", "eslint": "8.55.0", "eslint-config-airbnb": "19.0.4", "eslint-config-airbnb-typescript": "17.1.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "2.29.0", "eslint-plugin-jsx-a11y": "6.8.0", "eslint-plugin-prettier": "~4.2.1", "eslint-plugin-react": "7.33.2", "eslint-plugin-react-hooks": "4.6.0", "jest": "29.7.0", "localtunnel": "2.0.2", "ngrok": "^4.3.3", "nodemon": "3.0.2", "prettier": "~2.8.8", "prettier-eslint": "^16.1.2", "prettier-eslint-cli": "8.0.1", "ts-jest": "29.1.1", "ts-node": "10.9.1", "ts-node-dev": "2.0.0", "tsc-transpile-only": "^0.0.3", "typescript": "5.3.2"}, "nodemonConfig": {"ignore": ["storage/*", "node_modules/*", "browser_data/*"]}}
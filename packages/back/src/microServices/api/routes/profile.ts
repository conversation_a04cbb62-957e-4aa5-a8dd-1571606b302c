import { Router, Request } from 'express'
import { pick } from 'lodash'
import { Rules, validateWithConditionalRules } from '../../../core/middlewares/validate'
import userResource from '../../../core/resources/userResource'
import userTransformer from '../../../core/transformers/userTransformer'
import { promiseHandler } from '../../../core/utils/routing/resourceRouter'
import {
  string,
  hasLengthLesserThanOrEqual,
  boolean,
  hasLengthGreaterThanOrEqual,
  has<PERSON>pperCaseCharacter,
  hasLowerCaseCharacter,
  has<PERSON>igitCharacter,
  hasSpecialCharacter,
  notContainKnownPatternsInZxcvbnResult,
} from '../../../core/utils/validator/rules'
import accountResource from '../../../core/resources/accountResource'
import { fromReqRes } from '../../../core/utils/resource/transformerHelpers'
import accountTransformer from '../../../core/transformers/accountTransformer'
import AgnusMyPlan from '../../../core/services/agnus/AgnusMyPlan'
import hasPermission from '../../../core/middlewares/hasPermission'
import authInfoAppLogResource from '../../../core/resources/authInfoAppLogResource'
import config from '../../../core/config'
import { state as mongoDBState } from '../../../core/services/setupMongoDB'
import { removeAccessToken } from '../../../core/services/auth/authService'
import isOpenByRulesAndDate from '../../../core/utils/isOpenByRulesAndDate'
import getTimezoneMinutesOffset from '../../../core/utils/getTimezoneMinutesOffset'
import authHistoryResource from '../../../core/resources/authHistoryResource'
import { getIpFromRequest, getUserAgentFromRequest } from '../../../core/middlewares/ipRestriction'
import { subYears } from 'date-fns'
import queuedAsyncMap from '../../../core/utils/array/queuedAsyncMap'
import hasher from '../../../core/utils/crypt/hasher'
import ValidationError from '../../../core/utils/error/ValidationError'
import BadRequestHttpError from '../../../core/utils/error/BadRequestHttpError'
import { decryptTextForAccount } from '../../../core/services/crypt/accountCryptor'
import otpTokenIsValid from '../../../core/utils/otp/tokenIsValid'
import roleResource from '../../../core/resources/roleResource'
import HttpError from '../../../core/utils/error/HttpError'

export const conditionedRules = (request: Request, rules: Rules): Rules => {
  const ignoreStrengthPassword = (request.headers['ignore-strength-password'] || 'false') === 'true'

  if (config('allowIgnoreStrengthPassword') && ignoreStrengthPassword) return rules

  const strengthPasswordRules = [
    hasLengthGreaterThanOrEqual(8),
    hasUpperCaseCharacter(),
    hasLowerCaseCharacter(),
    hasDigitCharacter(),
    hasSpecialCharacter(),
    notContainKnownPatternsInZxcvbnResult(),
  ]
  return {
    ...rules,
    body: {
      ...rules?.body,
      password: [...(rules?.body?.password || []), ...strengthPasswordRules],
    },
  }
}

export const validateUpdate = validateWithConditionalRules(conditionedRules, {
  body: {
    name: [string, hasLengthLesserThanOrEqual(255)],
    email: [string, hasLengthLesserThanOrEqual(255)],
    password: [string, hasLengthLesserThanOrEqual(255)],
    isSuperAdmin: [boolean],
    isClientUser: [boolean],
  },
})

const controller = {
  getUser: promiseHandler(async (req, res) => {
    const { query } = req

    let hasRolePermissionQuery = false

    if (Array.isArray(query.include) && query.include.includes('permissions')) {
      const i = query.include.indexOf('permissions')

      if (i !== -1) {
        query.include = query.include.filter((_, index) => index !== i)
        hasRolePermissionQuery = true
      }
    }

    const user = await userResource.reload(res.locals.user, { ...query, cache: true })

    if (!user) throw new HttpError(401, 'User not found')

    if (hasRolePermissionQuery) {
      // Fazer separado para não gerar produto carteziano
      user.roles = await roleResource.findMany({
        include: [
          'permissions',
          {
            model: 'users',
            where: {
              id: user.id,
            },
            required: true,
            attributes: [],
          },
        ],
      })
    }

    return userTransformer(user, { byPassTwoFactor: true })
  }),
  getAccount: promiseHandler(async (req, res) => {
    const { query } = req
    const account = await accountResource.findById(res.locals.user.account.id, { ...query, cache: false })
    return accountTransformer(account)
  }),
  getAccountAmounts: promiseHandler(async (req, res) => {
    const accountId = res.locals?.user?.accountId

    if (!accountId) {
      throw new BadRequestHttpError(`Account ID is invalid for: ${accountId}`)
    }

    const filter = req.query?.filter
      ? String(req.query?.filter)
          .replace(/[^a-z.,]/g, '')
          .split(',')
      : []

    // Esta rota não deve retornar as quantidades de créditos para a Digisac
    return accountResource.getAccountAmounts(accountId, filter, ['credits.amounts'])
  }),
  update: promiseHandler(async (req, res) => {
    const { user } = res.locals
    const data = {
      ...pick(req.body, ['name', 'password', 'language', 'data', 'logoutFromApp', 'preferences']),
      ...(typeof req.body.otpAuthActive === 'boolean' &&
        !req.body.otpAuthActive &&
        user.otpAuthActive && { otpAuthActive: false }),
    }

    const changedPassword = !!data.password

    if (changedPassword) {
      const userHistory = await userResource.findById(user.id, {
        include: [
          {
            model: 'authHistory',
            where: {
              event: 'password_change',
              createdAt: {
                $gte: subYears(new Date(), 1),
              },
            },
            limit: 6,
            order: [['createdAt', 'desc']],
            required: false,
          },
        ],
      })

      await queuedAsyncMap(
        userHistory.authHistory,
        async (history) => {
          if (await hasher.compare(req.body.password, history.passwordHash)) {
            throw new ValidationError(null, 'Recently used password')
          }
        },
        1,
        false,
        true,
      )

      await authHistoryResource.create({
        userId: userHistory.id,
        accountId: userHistory.accountId,
        event: 'password_change',
        accessTokenId: res.locals.oauth?.token.accessTokenId,
        passwordHash: userHistory.password,
        branch: config('branch'),
        originIP: getIpFromRequest(req),
        originUA: getUserAgentFromRequest(req),
      })

      await removeAccessToken(userHistory.id)
    }

    const updatedUser = await userResource.update(res.locals.user, data)

    if (updatedUser.data?.fromApp && mongoDBState.isConnected()) {
      await authInfoAppLogResource
        .create({
          url: config('publicUrl'),
          username: updatedUser.email,
          name: updatedUser.name,
          accountId: updatedUser.accountId,
        })
        .catch(() => 'Error create log app')
    }
    return userTransformer(updatedUser, { byPassTwoFactor: true })
  }),
  updateStatus: promiseHandler(async (req, res) => {
    const { userId, status, client } = req.body

    const validateHour = async (): Promise<boolean> => {
      const user = await userResource.findById(userId, {
        attributes: ['timetableId'],
        include: [
          {
            model: 'timetable',
            attributes: ['workPlan'],
          },
          {
            model: 'account',
            attributes: ['settings'],
          },
        ],
      })
      if (!user?.timetableId) {
        return true
      }
      return isOpenByRulesAndDate(
        user?.timetable?.workPlan,
        new Date(),
        getTimezoneMinutesOffset(user?.account?.settings?.timezone),
      )
    }

    const statusToUpdate = status !== 'online' || (await validateHour()) ? status : 'offline'

    const user = await userResource.updateStatus(userId, statusToUpdate, client)
    return userTransformer(user)
  }),
  updateAccount: promiseHandler(async (req, res) => {
    const { accountId } = res.locals.user
    const defaultDepartmentId =
      req.body.defaultDepartmentId || (req.body.defaultDepartment && req.body.defaultDepartment.id)

    const account = await accountResource.findById(accountId)

    if (!req.body.settings.isPasswordExpirationActive) {
      await userResource.removeUserPasswordExpiresAt(accountId)
    }

    const data = {
      ...pick(req.body, ['name', 'settings', 'wizardProgress']),
      accountId,
      defaultDepartmentId,
    }

    return accountResource
      .update(account, data)
      .then((account) => accountResource.reload(account, req.query))
      .then(fromReqRes(req, res).transform(accountTransformer))
  }),
  reportFirstLogin: promiseHandler(async (req, res) => {
    const { user } = res.locals

    return userResource.updateById(user.id, {
      isFirstLogin: false,
    })
  }),
  takeover: promiseHandler(async (req, res) => {
    const { user } = res.locals

    const { client = 'web' } = req.body

    const updatedUser = await userResource.updateStatus(user.id, 'online', client)

    userResource.emitTakeover(updatedUser)
  }),
  extendAccountGracePeriod: promiseHandler(async (req, res) => {
    const { user } = res.locals

    return accountResource.extendGracePeriod(user.account, user)
  }),
  agnusMyPlanUrl: promiseHandler(async (req, res) => {
    const { user, oauth } = res.locals
    const digisacToken = oauth?.token?.accessToken

    const accountId = user.accountId
    const agnusMyPlan = new AgnusMyPlan()

    if (await agnusMyPlan.isCreditsControlEnabled(accountId)) {
      return agnusMyPlan.getPlanUrl_v2(accountId, digisacToken)
    }

    return agnusMyPlan.updateAmounts(accountId).then((_) => agnusMyPlan.getPlanUrl(accountId, user.name))
  }),
  registerToken: promiseHandler(async (req, res) => {
    await userResource.registerToken(res.locals.user, req.body)
  }),
  setOTPSecretKey: promiseHandler(async (req, res) => {
    const { secretKey = '' } = req.body
    if (secretKey.length !== 16) {
      throw new BadRequestHttpError('Invalid secret key. It must have 16 characters.')
    }
    await userResource.setOTPSecretKey(res.locals.user, secretKey)
  }),
  activateOTPAuth: promiseHandler(async (req, res) => {
    const { user } = res.locals
    const token = req.headers['x-digisac-otp'] || ''

    if (!token) {
      res.setHeader('x-digisac-otp', 'required')
      throw new BadRequestHttpError('OTP Token is required.')
    }

    const { otpSecretKey = '' } = user
    const account = await user.getAccount()
    const secretKey = await decryptTextForAccount(account, otpSecretKey)

    if (!otpTokenIsValid(token, secretKey)) {
      res.setHeader('x-digisac-otp', 'invalid')
      throw new BadRequestHttpError(`OTP Token ${token} is invalid.`)
    }

    await userResource.update(user, { otpAuthActive: true })

    return true
  }),
}

const router = Router()

router.get('/', controller.getUser)
router.get('/account', controller.getAccount)
router.get('/get-account-amounts', controller.getAccountAmounts)
router.put('/', validateUpdate, controller.update)
router.put('/account', controller.updateAccount)
router.post('/account/extend-grace-period', controller.extendAccountGracePeriod)
router.post('/one-signal/register-token', controller.registerToken)
router.get('/account/agnus-my-plan-url', hasPermission('myAccount.myPlan'), controller.agnusMyPlanUrl)
router.post('/report-login', controller.reportFirstLogin)
router.post('/takeover', controller.takeover)
router.put('/status', controller.updateStatus)
router.patch('/set-otp-secret-key', controller.setOTPSecretKey)
router.post('/activate-otp-auth', controller.activateOTPAuth)

export default router
